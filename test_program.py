#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三坐标问题记录程序的基本功能
"""

import os
import sys
from datetime import datetime

# 测试openpyxl是否可用
try:
    from openpyxl import Workbook, load_workbook
    print("✓ openpyxl库已正确安装")
except ImportError:
    print("✗ 错误：openpyxl库未安装，请运行: pip install openpyxl")
    sys.exit(1)

# 测试tkinter是否可用
try:
    import tkinter as tk
    from tkinter import ttk
    print("✓ tkinter库可用")
except ImportError:
    print("✗ 错误：tkinter库不可用")
    sys.exit(1)

def test_excel_creation():
    """测试Excel文件创建功能"""
    test_file = "test_三坐标问题记录.xlsx"
    
    try:
        # 删除测试文件（如果存在）
        if os.path.exists(test_file):
            os.remove(test_file)
        
        # 创建新的Excel文件
        wb = Workbook()
        ws = wb.active
        ws.title = "问题记录"
        
        # 设置表头
        headers = ["记录时间", "客户名称", "问题类型", "问题描述", "解决方案"]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 添加测试数据
        test_data = [
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "测试客户",
            "硬件问题",
            "这是一个测试问题描述",
            "这是测试解决方案"
        ]
        
        for col, value in enumerate(test_data, 1):
            ws.cell(row=2, column=col, value=value)
        
        wb.save(test_file)
        print(f"✓ Excel文件创建成功: {test_file}")
        
        # 测试读取
        wb2 = load_workbook(test_file)
        ws2 = wb2.active
        print(f"✓ Excel文件读取成功，包含 {ws2.max_row} 行数据")
        
        # 清理测试文件
        os.remove(test_file)
        print("✓ 测试文件已清理")
        
        return True
        
    except Exception as e:
        print(f"✗ Excel操作测试失败: {str(e)}")
        return False

def test_gui_creation():
    """测试GUI创建功能"""
    try:
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        # 创建一些基本控件
        label = ttk.Label(root, text="测试标签")
        label.pack(pady=10)
        
        entry_var = tk.StringVar()
        entry = ttk.Entry(root, textvariable=entry_var)
        entry.pack(pady=5)
        
        combo_var = tk.StringVar()
        combo = ttk.Combobox(root, textvariable=combo_var, 
                            values=["硬件问题", "软件问题", "图纸问题", "其他"])
        combo.pack(pady=5)
        
        text_widget = tk.Text(root, height=3, width=30)
        text_widget.pack(pady=5)
        
        button = ttk.Button(root, text="测试按钮")
        button.pack(pady=5)
        
        print("✓ GUI控件创建成功")
        
        # 立即销毁窗口（不显示）
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI创建测试失败: {str(e)}")
        return False

def main():
    """运行所有测试"""
    print("开始测试三坐标问题记录程序...")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # 测试Excel功能
    print("\n1. 测试Excel操作功能:")
    if test_excel_creation():
        tests_passed += 1
    
    # 测试GUI功能
    print("\n2. 测试GUI创建功能:")
    if test_gui_creation():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成: {tests_passed}/{total_tests} 项测试通过")
    
    if tests_passed == total_tests:
        print("✓ 所有测试通过！程序应该可以正常运行。")
        print("\n要启动主程序，请运行:")
        print("python coordinate_problem_recorder.py")
    else:
        print("✗ 部分测试失败，请检查环境配置。")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
