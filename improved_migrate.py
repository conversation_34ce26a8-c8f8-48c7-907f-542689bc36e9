#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的数据迁移脚本
更好地处理客户名称字段映射
"""

import os
import sys
from datetime import datetime

try:
    from openpyxl import load_workbook, Workbook
    from openpyxl.styles import Font, Alignment, PatternFill
except ImportError:
    print("请安装openpyxl库: pip install openpyxl")
    sys.exit(1)

def analyze_original_data():
    """分析原始数据结构"""
    original_file = "学员问题汇总.xlsx"
    
    if not os.path.exists(original_file):
        print(f"错误：找不到原文件 {original_file}")
        return None
    
    try:
        wb = load_workbook(original_file)
        ws = wb.active
        
        print(f"原文件分析：")
        print(f"  总行数: {ws.max_row}")
        print(f"  总列数: {ws.max_column}")
        
        # 读取表头
        headers = []
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=1, column=col).value
            if cell_value:
                headers.append(str(cell_value).strip())
            else:
                headers.append(f"列{col}")
        
        print("\n原文件表头详细信息：")
        for i, header in enumerate(headers, 1):
            print(f"  列{i}: {header}")
        
        # 分析前几行数据
        print("\n前3行数据示例：")
        for row in range(2, min(5, ws.max_row + 1)):
            print(f"  第{row-1}行:")
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=row, column=col).value
                if cell_value:
                    cell_str = str(cell_value).strip()
                    if len(cell_str) > 50:
                        cell_str = cell_str[:47] + "..."
                    print(f"    {headers[col-1]}: {cell_str}")
        
        return headers, ws
        
    except Exception as e:
        print(f"分析原文件时出错: {str(e)}")
        return None

def create_improved_migration():
    """创建改进的迁移文件"""
    result = analyze_original_data()
    if result is None:
        return False
    
    headers, ws = result
    
    # 让用户选择字段映射
    print("\n" + "=" * 60)
    print("请选择字段映射关系：")
    print("=" * 60)
    
    field_mapping = {}
    
    # 客户名称字段
    print(f"\n1. 客户名称字段：")
    for i, header in enumerate(headers, 1):
        print(f"   {i}. {header}")
    
    while True:
        try:
            choice = input(f"请选择客户名称对应的列号 (1-{len(headers)}, 或输入0跳过): ")
            choice = int(choice)
            if choice == 0:
                print("   跳过客户名称字段")
                break
            elif 1 <= choice <= len(headers):
                field_mapping['客户名称'] = choice - 1
                print(f"   客户名称 <- {headers[choice-1]}")
                break
            else:
                print(f"   请输入1到{len(headers)}之间的数字")
        except ValueError:
            print("   请输入有效的数字")
    
    # 问题类型字段
    print(f"\n2. 问题类型字段：")
    for i, header in enumerate(headers, 1):
        print(f"   {i}. {header}")
    
    while True:
        try:
            choice = input(f"请选择问题类型对应的列号 (1-{len(headers)}, 或输入0跳过): ")
            choice = int(choice)
            if choice == 0:
                print("   跳过问题类型字段，将设置为'其他'")
                break
            elif 1 <= choice <= len(headers):
                field_mapping['问题类型'] = choice - 1
                print(f"   问题类型 <- {headers[choice-1]}")
                break
            else:
                print(f"   请输入1到{len(headers)}之间的数字")
        except ValueError:
            print("   请输入有效的数字")
    
    # 问题描述字段
    print(f"\n3. 问题描述字段：")
    for i, header in enumerate(headers, 1):
        print(f"   {i}. {header}")
    
    while True:
        try:
            choice = input(f"请选择问题描述对应的列号 (1-{len(headers)}): ")
            choice = int(choice)
            if 1 <= choice <= len(headers):
                field_mapping['问题描述'] = choice - 1
                print(f"   问题描述 <- {headers[choice-1]}")
                break
            else:
                print(f"   请输入1到{len(headers)}之间的数字")
        except ValueError:
            print("   请输入有效的数字")
    
    # 解决方案字段
    print(f"\n4. 解决方案字段：")
    for i, header in enumerate(headers, 1):
        print(f"   {i}. {header}")
    
    while True:
        try:
            choice = input(f"请选择解决方案对应的列号 (1-{len(headers)}, 或输入0跳过): ")
            choice = int(choice)
            if choice == 0:
                print("   跳过解决方案字段")
                break
            elif 1 <= choice <= len(headers):
                field_mapping['解决方案'] = choice - 1
                print(f"   解决方案 <- {headers[choice-1]}")
                break
            else:
                print(f"   请输入1到{len(headers)}之间的数字")
        except ValueError:
            print("   请输入有效的数字")
    
    # 时间字段
    print(f"\n5. 记录时间字段：")
    for i, header in enumerate(headers, 1):
        print(f"   {i}. {header}")
    
    while True:
        try:
            choice = input(f"请选择记录时间对应的列号 (1-{len(headers)}, 或输入0使用当前时间): ")
            choice = int(choice)
            if choice == 0:
                print("   使用当前时间作为记录时间")
                break
            elif 1 <= choice <= len(headers):
                field_mapping['记录时间'] = choice - 1
                print(f"   记录时间 <- {headers[choice-1]}")
                break
            else:
                print(f"   请输入1到{len(headers)}之间的数字")
        except ValueError:
            print("   请输入有效的数字")
    
    # 开始转换数据
    print(f"\n开始数据转换...")
    
    try:
        # 创建新工作簿
        new_wb = Workbook()
        new_ws = new_wb.active
        new_ws.title = "问题记录"
        
        # 新格式的表头
        new_headers = ["记录时间", "客户名称", "问题类型", "问题描述", "解决方案"]
        
        # 设置表头
        for col, header in enumerate(new_headers, 1):
            cell = new_ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 设置列宽
        column_widths = [20, 15, 15, 40, 40]
        for col, width in enumerate(column_widths, 1):
            new_ws.column_dimensions[new_ws.cell(row=1, column=col).column_letter].width = width
        
        # 转换数据
        converted_count = 0
        for row in range(2, ws.max_row + 1):
            # 读取原行数据
            row_data = []
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=row, column=col).value
                if cell_value is not None:
                    row_data.append(str(cell_value).strip())
                else:
                    row_data.append("")
            
            # 检查是否为有效行
            if not any(cell.strip() for cell in row_data):
                continue
            
            # 准备新行数据
            new_row = [""] * len(new_headers)
            
            # 记录时间
            if '记录时间' in field_mapping:
                time_col = field_mapping['记录时间']
                if time_col < len(row_data) and row_data[time_col]:
                    new_row[0] = row_data[time_col]
                else:
                    new_row[0] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            else:
                new_row[0] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 客户名称
            if '客户名称' in field_mapping:
                customer_col = field_mapping['客户名称']
                if customer_col < len(row_data):
                    new_row[1] = row_data[customer_col]
            
            # 问题类型
            if '问题类型' in field_mapping:
                type_col = field_mapping['问题类型']
                if type_col < len(row_data):
                    problem_type = row_data[type_col]
                    # 映射到新的问题类型
                    if any(keyword in problem_type for keyword in ['硬件', '设备', '机器']):
                        new_row[2] = "硬件问题"
                    elif any(keyword in problem_type for keyword in ['软件', '程序', '系统']):
                        new_row[2] = "软件问题"
                    elif any(keyword in problem_type for keyword in ['图纸', '图形', '绘图']):
                        new_row[2] = "图纸问题"
                    else:
                        new_row[2] = problem_type if problem_type else "其他"
                else:
                    new_row[2] = "其他"
            else:
                new_row[2] = "其他"
            
            # 问题描述
            if '问题描述' in field_mapping:
                desc_col = field_mapping['问题描述']
                if desc_col < len(row_data):
                    new_row[3] = row_data[desc_col]
            
            # 解决方案
            if '解决方案' in field_mapping:
                solution_col = field_mapping['解决方案']
                if solution_col < len(row_data):
                    new_row[4] = row_data[solution_col]
            
            # 只保存有意义的数据（至少有问题描述）
            if new_row[3]:
                # 写入新行
                for col, value in enumerate(new_row, 1):
                    new_ws.cell(row=converted_count + 2, column=col, value=value)
                converted_count += 1
        
        # 保存文件
        output_file = "三坐标问题记录_完整迁移.xlsx"
        new_wb.save(output_file)
        
        print(f"\n数据转换完成！")
        print(f"成功转换 {converted_count} 条记录")
        print(f"新文件已保存为: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"数据转换时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("改进的数据迁移工具")
    print("=" * 60)
    
    if create_improved_migration():
        print("\n" + "=" * 60)
        print("迁移完成！")
        print("\n请按以下步骤操作：")
        print("1. 检查生成的 '三坐标问题记录_完整迁移.xlsx' 文件")
        print("2. 如果数据正确，请手动将其重命名为 '三坐标问题记录.xlsx'")
        print("3. 然后就可以使用 coordinate_problem_recorder.py 程序了")
    else:
        print("迁移失败！")
    
    return True

if __name__ == "__main__":
    main()
