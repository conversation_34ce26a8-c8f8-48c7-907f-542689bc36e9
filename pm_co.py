#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三坐标问题记录程序
用于记录客户在三坐标使用过程中出现的问题，并保存到Excel文件中
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import os
import sys

try:
    from openpyxl import Workbook, load_workbook
    from openpyxl.styles import Font, Alignment, PatternFill
except ImportError:
    print("请安装openpyxl库: pip install openpyxl")
    sys.exit(1)


class CoordinateProblemRecorder:
    def __init__(self, root):
        self.root = root
        self.root.title("三坐标问题记录系统")
        self.root.geometry("600x600")
        self.root.resizable(True, True)
        
        # 默认Excel文件路径
        self.excel_file = "三坐标问题记录.xlsx"
        
        self.setup_ui()
        self.create_excel_if_not_exists()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="三坐标问题记录系统", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 文件选择框架
        file_frame = ttk.LabelFrame(main_frame, text="Excel文件设置", padding="5")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="保存文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.file_var = tk.StringVar(value=self.excel_file)
        file_entry = ttk.Entry(file_frame, textvariable=self.file_var, state="readonly")
        file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(file_frame, text="选择文件", 
                  command=self.select_file).grid(row=0, column=2)
        
        # 输入框架
        input_frame = ttk.LabelFrame(main_frame, text="问题信息录入", padding="10")
        input_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)
        input_frame.columnconfigure(3, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 客户名称
        ttk.Label(input_frame, text="客户名称(可选):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.customer_var = tk.StringVar()
        customer_entry = ttk.Entry(input_frame, textvariable=self.customer_var, width=40)
        customer_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 问题类型
        ttk.Label(input_frame, text="问题类型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.problem_type_var = tk.StringVar()
        self.problem_type_combo = ttk.Combobox(input_frame, textvariable=self.problem_type_var,
                                         values=["硬件问题", "软件问题", "图纸问题", "培训学员问题", "其他"])
        self.problem_type_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        self.problem_type_combo.bind("<<ComboboxSelected>>", self.on_problem_type_change)

        # 自定义问题类型输入框（当选择"其他"时显示）
        self.custom_type_label = ttk.Label(input_frame, text="自定义类型:")
        self.custom_type_var = tk.StringVar()
        self.custom_type_entry = ttk.Entry(input_frame, textvariable=self.custom_type_var, width=40)
        
        # 问题描述
        ttk.Label(input_frame, text="问题描述:").grid(row=2, column=0, sticky=(tk.W, tk.N), pady=5)
        self.problem_desc_text = tk.Text(input_frame, height=6, width=50, wrap=tk.WORD)
        self.problem_desc_text.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(10, 0))

        # 滚动条
        scrollbar = ttk.Scrollbar(input_frame, orient=tk.VERTICAL, command=self.problem_desc_text.yview)
        scrollbar.grid(row=2, column=2, sticky=(tk.N, tk.S), pady=5)
        self.problem_desc_text.configure(yscrollcommand=scrollbar.set)

        # 解决方案
        ttk.Label(input_frame, text="解决方案:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=5)
        self.solution_text = tk.Text(input_frame, height=4, width=50, wrap=tk.WORD)
        self.solution_text.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(10, 0))

        # 解决方案滚动条
        solution_scrollbar = ttk.Scrollbar(input_frame, orient=tk.VERTICAL, command=self.solution_text.yview)
        solution_scrollbar.grid(row=3, column=2, sticky=(tk.N, tk.S), pady=5)
        self.solution_text.configure(yscrollcommand=solution_scrollbar.set)
        
        # 配置输入框架的行权重
        input_frame.rowconfigure(2, weight=1)
        input_frame.rowconfigure(3, weight=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        # 保存按钮
        save_btn = ttk.Button(button_frame, text="保存记录", command=self.save_record,
                             style="Accent.TButton")
        save_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空按钮
        clear_btn = ttk.Button(button_frame, text="清空表单", command=self.clear_form)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 查看记录按钮
        view_btn = ttk.Button(button_frame, text="查看记录", command=self.view_records)
        view_btn.pack(side=tk.LEFT)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪", relief=tk.SUNKEN)
        self.status_label.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def select_file(self):
        """选择Excel文件"""
        filename = filedialog.asksaveasfilename(
            title="选择或创建Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.excel_file = filename
            self.file_var.set(filename)
            self.create_excel_if_not_exists()
    
    def create_excel_if_not_exists(self):
        """如果Excel文件不存在则创建"""
        try:
            if not os.path.exists(self.excel_file):
                wb = Workbook()
                # 删除默认工作表
                wb.remove(wb.active)

                # 创建不同问题类型的工作表
                sheet_names = ["硬件问题", "软件问题", "图纸问题", "培训学员问题", "其他问题"]

                for sheet_name in sheet_names:
                    ws = wb.create_sheet(title=sheet_name)

                    # 设置表头
                    headers = ["记录时间", "客户名称", "问题描述", "解决方案"]
                    for col, header in enumerate(headers, 1):
                        cell = ws.cell(row=1, column=col, value=header)
                        cell.font = Font(bold=True)
                        cell.alignment = Alignment(horizontal='center')
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

                    # 设置列宽
                    column_widths = [20, 15, 40, 40]
                    for col, width in enumerate(column_widths, 1):
                        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width

                wb.save(self.excel_file)
                self.update_status(f"已创建新的Excel文件: {self.excel_file}")
            else:
                # 检查现有文件是否有所需的工作表
                self.ensure_worksheets_exist()
                self.update_status(f"使用现有Excel文件: {self.excel_file}")
        except Exception as e:
            messagebox.showerror("错误", f"创建Excel文件时出错: {str(e)}")

    def ensure_worksheets_exist(self):
        """确保Excel文件中存在所需的工作表"""
        try:
            wb = load_workbook(self.excel_file)
            sheet_names = ["硬件问题", "软件问题", "图纸问题", "培训学员问题", "其他问题"]

            for sheet_name in sheet_names:
                if sheet_name not in wb.sheetnames:
                    ws = wb.create_sheet(title=sheet_name)

                    # 设置表头
                    headers = ["记录时间", "客户名称", "问题描述", "解决方案"]
                    for col, header in enumerate(headers, 1):
                        cell = ws.cell(row=1, column=col, value=header)
                        cell.font = Font(bold=True)
                        cell.alignment = Alignment(horizontal='center')
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

                    # 设置列宽
                    column_widths = [20, 15, 40, 40]
                    for col, width in enumerate(column_widths, 1):
                        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width

            wb.save(self.excel_file)
        except Exception as e:
            print(f"确保工作表存在时出错: {str(e)}")
    
    def save_record(self):
        """保存记录到Excel"""
        # 验证输入
        if not self.problem_type_var.get().strip():
            messagebox.showwarning("警告", "请选择问题类型")
            return

        if not self.problem_desc_text.get("1.0", tk.END).strip():
            messagebox.showwarning("警告", "请输入问题描述")
            return

        try:
            # 加载工作簿
            wb = load_workbook(self.excel_file)

            # 获取问题类型，如果是"其他"则使用自定义输入
            problem_type = self.problem_type_var.get()
            if problem_type == "其他" and self.custom_type_var.get().strip():
                problem_type = self.custom_type_var.get().strip()
                sheet_name = "其他问题"
            else:
                sheet_name = problem_type

            # 确保工作表存在
            if sheet_name not in wb.sheetnames:
                # 如果工作表不存在，创建新的工作表
                ws = wb.create_sheet(title=sheet_name)
                # 设置表头
                headers = ["记录时间", "客户名称", "问题描述", "解决方案"]
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center')
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

                # 设置列宽
                column_widths = [20, 15, 40, 40]
                for col, width in enumerate(column_widths, 1):
                    ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
            else:
                ws = wb[sheet_name]

            # 找到下一个空行
            next_row = ws.max_row + 1

            # 准备数据（不包含问题类型，因为已经通过工作表名称体现）
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            customer_name = self.customer_var.get().strip() if self.customer_var.get().strip() else "未填写"
            data = [
                current_time,
                customer_name,
                self.problem_desc_text.get("1.0", tk.END).strip(),
                self.solution_text.get("1.0", tk.END).strip()
            ]

            # 写入数据
            for col, value in enumerate(data, 1):
                ws.cell(row=next_row, column=col, value=value)

            # 保存文件
            wb.save(self.excel_file)

            self.update_status(f"记录已保存到 '{sheet_name}' 工作表第 {next_row} 行")
            messagebox.showinfo("成功", f"问题记录已成功保存到 '{sheet_name}' 工作表中！")

            # 询问是否清空表单
            if messagebox.askyesno("提示", "是否清空表单以便录入下一条记录？"):
                self.clear_form()

        except Exception as e:
            messagebox.showerror("错误", f"保存记录时出错: {str(e)}")
    
    def clear_form(self):
        """清空表单"""
        self.customer_var.set("")
        self.problem_type_var.set("")
        self.custom_type_var.set("")
        self.problem_desc_text.delete("1.0", tk.END)
        self.solution_text.delete("1.0", tk.END)
        self.hide_custom_type_input()
        self.update_status("表单已清空")
    
    def view_records(self):
        """查看记录（打开Excel文件）"""
        try:
            if os.path.exists(self.excel_file):
                os.startfile(self.excel_file)  # Windows
                self.update_status("已打开Excel文件")
            else:
                messagebox.showwarning("警告", "Excel文件不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开Excel文件时出错: {str(e)}")
    
    def on_problem_type_change(self, event=None):
        """当问题类型改变时的处理"""
        if self.problem_type_var.get() == "其他":
            self.show_custom_type_input()
        else:
            self.hide_custom_type_input()

    def show_custom_type_input(self):
        """显示自定义问题类型输入框"""
        self.custom_type_label.grid(row=1, column=2, sticky=tk.W, pady=5, padx=(10, 0))
        self.custom_type_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))

    def hide_custom_type_input(self):
        """隐藏自定义问题类型输入框"""
        self.custom_type_label.grid_remove()
        self.custom_type_entry.grid_remove()

    def update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=message)
        self.root.update_idletasks()


def main():
    """主函数"""
    root = tk.Tk()
    app = CoordinateProblemRecorder(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    root.mainloop()


if __name__ == "__main__":
    main()
