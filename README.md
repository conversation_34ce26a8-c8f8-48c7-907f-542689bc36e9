# 三坐标问题记录系统

这是一个用于记录客户在三坐标使用过程中出现问题的桌面应用程序。程序提供了友好的图形界面，可以方便地录入问题信息并自动保存到Excel文件中。

## 功能特点

- **图形化界面**: 使用tkinter构建的直观用户界面
- **Excel存储**: 自动将问题记录保存到Excel文件中，支持追加模式（不覆盖之前的记录）
- **数据验证**: 确保必要字段不为空
- **文件管理**: 可以选择不同的Excel文件进行保存
- **记录查看**: 一键打开Excel文件查看历史记录

## 安装要求

- Python 3.6 或更高版本
- openpyxl 库（用于Excel文件操作）

## 安装步骤

1. 确保已安装Python 3.6+
2. 安装依赖库：
   ```bash
   pip install -r requirements.txt
   ```
   或者直接安装：
   ```bash
   pip install openpyxl
   ```

## 使用方法

1. 首先可以运行测试程序检查环境：
   ```bash
   python test_program.py
   ```

2. 运行主程序：
   ```bash
   python coordinate_problem_recorder.py
   ```

2. 在界面中填写以下信息：
   - **客户名称**: 必填项，客户的名称或公司名
   - **问题类型**: 从下拉菜单选择问题类型（硬件问题、软件问题、图纸问题、其他）
     - 选择"其他"时，会显示自定义输入框，可以手动输入具体的问题类型
   - **问题描述**: 必填项，详细描述遇到的问题
   - **解决方案**: 记录解决问题的方法或步骤

3. 点击"保存记录"按钮将信息保存到Excel文件

4. 使用"查看记录"按钮可以直接打开Excel文件查看所有历史记录

## Excel文件格式

程序会自动创建包含以下列的Excel文件：
- 记录时间
- 客户名称
- 问题类型
- 问题描述
- 解决方案

## 注意事项

- 程序默认会在当前目录创建"三坐标问题记录.xlsx"文件
- 可以通过"选择文件"按钮指定其他Excel文件路径
- 每次保存都会追加到文件末尾，不会覆盖之前的记录
- 建议定期备份Excel文件以防数据丢失

## 故障排除

如果遇到以下问题：

1. **ImportError: No module named 'openpyxl'**
   - 解决方法：运行 `pip install openpyxl`

2. **无法打开Excel文件**
   - 确保系统已安装Excel或其他支持.xlsx格式的软件
   - 检查文件路径是否正确

3. **保存失败**
   - 确保有写入权限
   - 检查Excel文件是否被其他程序占用

## 技术支持

如有问题或建议，请联系开发团队。
