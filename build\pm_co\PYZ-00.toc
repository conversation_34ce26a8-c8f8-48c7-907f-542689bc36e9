('F:\\test\\build\\pm_co\\PYZ-00.pyz',
 [('PIL', 'f:\\python\\lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'f:\\python\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'f:\\python\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'f:\\python\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'f:\\python\\lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'f:\\python\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'f:\\python\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'f:\\python\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'f:\\python\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'f:\\python\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'f:\\python\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'f:\\python\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'f:\\python\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'f:\\python\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'f:\\python\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'f:\\python\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'f:\\python\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'f:\\python\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'f:\\python\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'f:\\python\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'f:\\python\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'f:\\python\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'f:\\python\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'f:\\python\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'f:\\python\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'f:\\python\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util', 'f:\\python\\lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'f:\\python\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'f:\\python\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'f:\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'f:\\python\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'f:\\python\\lib\\_bootsubprocess.py', 'PYMODULE'),
  ('_compat_pickle', 'f:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'f:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'f:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'f:\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'f:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'f:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'f:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'f:\\python\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'f:\\python\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'f:\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'f:\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'f:\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'f:\\python\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'f:\\python\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'f:\\python\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'f:\\python\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'f:\\python\\lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'f:\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'f:\\python\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'f:\\python\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'f:\\python\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'f:\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'f:\\python\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'f:\\python\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'f:\\python\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'f:\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'f:\\python\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'f:\\python\\lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'f:\\python\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'f:\\python\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.tasks', 'f:\\python\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'f:\\python\\lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.transports', 'f:\\python\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'f:\\python\\lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'f:\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'f:\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'f:\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'f:\\python\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'f:\\python\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'f:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'f:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'f:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'f:\\python\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'f:\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'f:\\python\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'f:\\python\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'f:\\python\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'f:\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'f:\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'f:\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'f:\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'f:\\python\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'f:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'f:\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'f:\\python\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'f:\\python\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'f:\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'f:\\python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('datetime', 'f:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'f:\\python\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'f:\\python\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'f:\\python\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'f:\\python\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'f:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'f:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'f:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'f:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'f:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'f:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'f:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'f:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'f:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'f:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'f:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'f:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'f:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'f:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'f:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'f:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'f:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'f:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'f:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'f:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'f:\\python\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'f:\\python\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'f:\\python\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'f:\\python\\lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'f:\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'f:\\python\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'f:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'f:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'f:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'f:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'f:\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'f:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'f:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'f:\\python\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'f:\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'f:\\python\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'f:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'f:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'f:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'f:\\python\\lib\\http\\server.py', 'PYMODULE'),
  ('importlib', 'f:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'f:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'f:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'f:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'f:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata', 'f:\\python\\lib\\importlib\\metadata.py', 'PYMODULE'),
  ('importlib.util', 'f:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'f:\\python\\lib\\inspect.py', 'PYMODULE'),
  ('json', 'f:\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'f:\\python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'f:\\python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'f:\\python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'f:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'f:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'f:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'f:\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'f:\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'f:\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'f:\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'f:\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'f:\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'f:\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'f:\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'f:\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'f:\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'f:\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'f:\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'f:\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'f:\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'f:\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'f:\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'f:\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'f:\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'f:\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'f:\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'f:\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'f:\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'f:\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'f:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'f:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'f:\\python\\lib\\numbers.py', 'PYMODULE'),
  ('numpy', 'f:\\python\\lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'f:\\python\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._core',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'f:\\python\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'f:\\python\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'f:\\python\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'f:\\python\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'f:\\python\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'f:\\python\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'f:\\python\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'f:\\python\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'f:\\python\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'f:\\python\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'f:\\python\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'f:\\python\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'f:\\python\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'f:\\python\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'f:\\python\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'f:\\python\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'f:\\python\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'f:\\python\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'f:\\python\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'f:\\python\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'f:\\python\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'f:\\python\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'f:\\python\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'f:\\python\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'f:\\python\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'f:\\python\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'f:\\python\\lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'f:\\python\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'f:\\python\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'f:\\python\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'f:\\python\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'f:\\python\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'f:\\python\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'f:\\python\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'f:\\python\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'f:\\python\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'f:\\python\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'f:\\python\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'f:\\python\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'f:\\python\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'f:\\python\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'f:\\python\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'f:\\python\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'f:\\python\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'f:\\python\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'f:\\python\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'f:\\python\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'f:\\python\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'f:\\python\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'f:\\python\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'f:\\python\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'f:\\python\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'f:\\python\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'f:\\python\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'f:\\python\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'f:\\python\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'f:\\python\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'f:\\python\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'f:\\python\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'f:\\python\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'f:\\python\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'f:\\python\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'f:\\python\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'f:\\python\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'f:\\python\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'f:\\python\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'f:\\python\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'f:\\python\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'f:\\python\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'f:\\python\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'f:\\python\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'f:\\python\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'f:\\python\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'f:\\python\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'f:\\python\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'f:\\python\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'f:\\python\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'f:\\python\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'f:\\python\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'f:\\python\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'f:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'f:\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'f:\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'f:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'f:\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'f:\\python\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'f:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'f:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'f:\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'f:\\python\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'f:\\python\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('queue', 'f:\\python\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'f:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'f:\\python\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'f:\\python\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'f:\\python\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'f:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'f:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'f:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'f:\\python\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'f:\\python\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'f:\\python\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'f:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'f:\\python\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'f:\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'f:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'f:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'f:\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'f:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'f:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'f:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'f:\\python\\lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'f:\\python\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'f:\\python\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'f:\\python\\lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'f:\\python\\lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog', 'f:\\python\\lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.messagebox', 'f:\\python\\lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.ttk', 'f:\\python\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'f:\\python\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'f:\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'f:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'f:\\python\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'f:\\python\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'f:\\python\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'f:\\python\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'f:\\python\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'f:\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'f:\\python\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'f:\\python\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'f:\\python\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result', 'f:\\python\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'f:\\python\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'f:\\python\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'f:\\python\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'f:\\python\\lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'f:\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'f:\\python\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'f:\\python\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'f:\\python\\lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'f:\\python\\lib\\urllib\\response.py', 'PYMODULE'),
  ('uu', 'f:\\python\\lib\\uu.py', 'PYMODULE'),
  ('webbrowser', 'f:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'f:\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree', 'f:\\python\\lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'f:\\python\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'f:\\python\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'f:\\python\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'f:\\python\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'f:\\python\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'f:\\python\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'f:\\python\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'f:\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'f:\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'f:\\python\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'f:\\python\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'f:\\python\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'f:\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'f:\\python\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('yaml', 'f:\\python\\lib\\site-packages\\yaml\\__init__.py', 'PYMODULE'),
  ('yaml.composer',
   'f:\\python\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'f:\\python\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml', 'f:\\python\\lib\\site-packages\\yaml\\cyaml.py', 'PYMODULE'),
  ('yaml.dumper',
   'f:\\python\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'f:\\python\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error', 'f:\\python\\lib\\site-packages\\yaml\\error.py', 'PYMODULE'),
  ('yaml.events',
   'f:\\python\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'f:\\python\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes', 'f:\\python\\lib\\site-packages\\yaml\\nodes.py', 'PYMODULE'),
  ('yaml.parser',
   'f:\\python\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'f:\\python\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'f:\\python\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'f:\\python\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'f:\\python\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'f:\\python\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'f:\\python\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'f:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'f:\\python\\lib\\zipimport.py', 'PYMODULE')])
